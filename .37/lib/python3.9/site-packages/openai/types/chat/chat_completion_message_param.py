# File generated from our OpenAPI spec by Stainless.

from __future__ import annotations

from typing import Optional
from typing_extensions import Literal, Required, TypedDict

__all__ = ["ChatCompletionMessageParam", "FunctionCall"]


class FunctionCall(TypedDict, total=False):
    arguments: Required[str]
    """
    The arguments to call the function with, as generated by the model in JSON
    format. Note that the model does not always generate valid JSON, and may
    hallucinate parameters not defined by your function schema. Validate the
    arguments in your code before calling your function.
    """

    name: Required[str]
    """The name of the function to call."""


class ChatCompletionMessageParam(TypedDict, total=False):
    content: Required[Optional[str]]
    """The contents of the message.

    `content` is required for all messages, and may be null for assistant messages
    with function calls.
    """

    role: Required[Literal["system", "user", "assistant", "function"]]
    """The role of the messages author.

    One of `system`, `user`, `assistant`, or `function`.
    """

    function_call: FunctionCall
    """
    The name and arguments of a function that should be called, as generated by the
    model.
    """

    name: str
    """The name of the author of this message.

    `name` is required if role is `function`, and it should be the name of the
    function whose response is in the `content`. May contain a-z, A-Z, 0-9, and
    underscores, with a maximum length of 64 characters.
    """
