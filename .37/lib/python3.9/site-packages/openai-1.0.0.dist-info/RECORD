../../../bin/openai,sha256=mqdy9ij7Y54XCy6lxX9uwsZcWOS_a2KM-g7Lg2LLG08,233
openai-1.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
openai-1.0.0.dist-info/METADATA,sha256=9RwoMtpxEoDCUaKnPa80GYM0kQiVNf_UZZU8zVMjH3g,16749
openai-1.0.0.dist-info/RECORD,,
openai-1.0.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai-1.0.0.dist-info/WHEEL,sha256=9QBuHhg6FNW7lppboF2vKVbCGTVzsFykgRQjjlajrhA,87
openai-1.0.0.dist-info/entry_points.txt,sha256=kAYhQEmziJwsKs5raYAIOvJ2LWmbz5dulEXOzsY71ro,43
openai-1.0.0.dist-info/licenses/LICENSE,sha256=Sa73AcxOcVUuCfzKf62Fu6hK2T7keXrLoEPQPTErZTY,11336
openai/__init__.py,sha256=VhekyCcQ4Jhwp1nc0X_1TLMgu1naQcoVVTKBpKij2Gw,9435
openai/__main__.py,sha256=bYt9eEaoRQWdejEHFD8REx9jxVEdZptECFsV7F49Ink,30
openai/__pycache__/__init__.cpython-39.pyc,,
openai/__pycache__/__main__.cpython-39.pyc,,
openai/__pycache__/_base_client.cpython-39.pyc,,
openai/__pycache__/_client.cpython-39.pyc,,
openai/__pycache__/_compat.cpython-39.pyc,,
openai/__pycache__/_constants.cpython-39.pyc,,
openai/__pycache__/_exceptions.cpython-39.pyc,,
openai/__pycache__/_files.cpython-39.pyc,,
openai/__pycache__/_models.cpython-39.pyc,,
openai/__pycache__/_module_client.cpython-39.pyc,,
openai/__pycache__/_qs.cpython-39.pyc,,
openai/__pycache__/_resource.cpython-39.pyc,,
openai/__pycache__/_response.cpython-39.pyc,,
openai/__pycache__/_streaming.cpython-39.pyc,,
openai/__pycache__/_types.cpython-39.pyc,,
openai/__pycache__/_version.cpython-39.pyc,,
openai/__pycache__/pagination.cpython-39.pyc,,
openai/__pycache__/version.cpython-39.pyc,,
openai/_base_client.py,sha256=h5fMfb-fDw8n6FOwg-EvtoC5QPS3_eIxtEfTddmcDjw,56069
openai/_client.py,sha256=aUtG9-Huj4d9qla_N4ScRJ2VwWTh_W8v4z9u389R6x8,18798
openai/_compat.py,sha256=pofRsaU2XT43Ij6aMRr3XfKgIVIJfAEUeElVFaE4cTA,5102
openai/_constants.py,sha256=DAtwhh5HUKB7BBQiqJvZ93P2ME0x5-vFzy8hhvl3WEA,315
openai/_exceptions.py,sha256=jLL5mzvGA1U_JF6OqTGlGkiSu_klBgrJxW6ed0Dw9Kg,3575
openai/_extras/__init__.py,sha256=PpHi-jjIVLWp4G1f5AfNQRJnoCxKiYU9CVMAyN-Crwo,131
openai/_extras/__pycache__/__init__.cpython-39.pyc,,
openai/_extras/__pycache__/_common.cpython-39.pyc,,
openai/_extras/__pycache__/numpy_proxy.cpython-39.pyc,,
openai/_extras/__pycache__/pandas_proxy.cpython-39.pyc,,
openai/_extras/_common.py,sha256=NWWtgbdJsO3hQGQxaXGfVk0LjeIE5AFZ8VS_795hhMc,364
openai/_extras/numpy_proxy.py,sha256=GVVSyABlGzdtF3xfGc5JkI801IsiN3VkMIYfWs2iVf0,834
openai/_extras/pandas_proxy.py,sha256=GV_R1ysbgq8y9WMSFQOhxvviz7CFrocj-oweX1v8FlA,672
openai/_files.py,sha256=hub0XrVXrumcRt_jWrnqKG_gUcbH_WylABtAogtQQYg,3468
openai/_models.py,sha256=QudYFsYWWaekdAvWjhCK3FOpfawICzt5d2PS7CKdkHE,16016
openai/_module_client.py,sha256=UUoaAHYivZsKrcxVf5hBfwHM9lKLWgs2IzMJZIyo0nA,2513
openai/_qs.py,sha256=AOkSz4rHtK4YI3ZU_kzea-zpwBUgEY8WniGmTPyEimc,4846
openai/_resource.py,sha256=F_pXU86CNv_FFBPjW7Pwcr4wOkcBaEEkoeQOGrwLlXY,1070
openai/_response.py,sha256=zF8CHtqUoheb_aKnYOhSZNZH1jGbeSFI8NwiqCAg6k8,9022
openai/_streaming.py,sha256=h7oNmOA-emDD6z-fA4qERuhyd65KFQVHSFJXqLKVVbI,6844
openai/_types.py,sha256=vQq9V6SI19sySmyTmlySJp1CzHxmMY1I7IGcm0ibo7c,9530
openai/_utils/__init__.py,sha256=1DSY01oDA6kU6h8nWhj9GcbuHS-GAIiEW-W8JDx6n7c,1848
openai/_utils/__pycache__/__init__.cpython-39.pyc,,
openai/_utils/__pycache__/_logs.cpython-39.pyc,,
openai/_utils/__pycache__/_proxy.cpython-39.pyc,,
openai/_utils/__pycache__/_transform.cpython-39.pyc,,
openai/_utils/__pycache__/_utils.cpython-39.pyc,,
openai/_utils/_logs.py,sha256=sFA_NejuNObTGGbfsXC03I38mrT9HjsgAJx4d3GP0ok,774
openai/_utils/_proxy.py,sha256=zivv4nHupudnA3kdBqFg9xRLDaObmeY8l97Tcfg6bN4,1573
openai/_utils/_transform.py,sha256=FK4KetKcbodbsSay9prvdA3aFwBaOGZ6yDKHz6LYS7E,7059
openai/_utils/_utils.py,sha256=zZzmKMuFXpDqB4i9r-WodSzMFZihbemcwdHPGxYa1q4,11751
openai/_version.py,sha256=-j7iPASPeQpK1_76L7g-L4RH0w8XN4VJq57TkCMlrbU,97
openai/cli/__init__.py,sha256=soGgtqyomgddl92H0KJRqHqGuaXIaghq86qkzLuVp7U,31
openai/cli/__pycache__/__init__.cpython-39.pyc,,
openai/cli/__pycache__/_cli.cpython-39.pyc,,
openai/cli/__pycache__/_errors.cpython-39.pyc,,
openai/cli/__pycache__/_models.cpython-39.pyc,,
openai/cli/__pycache__/_progress.cpython-39.pyc,,
openai/cli/__pycache__/_utils.cpython-39.pyc,,
openai/cli/_api/__init__.py,sha256=cj92MZq-9_1PQM8A4TQVsqKn5mcTDAGxHllJ0UvJOPE,58
openai/cli/_api/__pycache__/__init__.cpython-39.pyc,,
openai/cli/_api/__pycache__/_main.cpython-39.pyc,,
openai/cli/_api/__pycache__/audio.cpython-39.pyc,,
openai/cli/_api/__pycache__/completions.cpython-39.pyc,,
openai/cli/_api/__pycache__/files.cpython-39.pyc,,
openai/cli/_api/__pycache__/image.cpython-39.pyc,,
openai/cli/_api/__pycache__/models.cpython-39.pyc,,
openai/cli/_api/_main.py,sha256=5yyfLURqCEaAN8B61gHaqVAaYgtyb9Xq0ncQ3P2BAh0,451
openai/cli/_api/audio.py,sha256=m-L3djPutDEjHWzEeL70Ov1xTqdrrJhB0i1AWVRO8NA,3321
openai/cli/_api/chat/__init__.py,sha256=MhFUQH9F6QCtbPMlbsU_DWTd7wc5DSCZ7Wy3FBGVij0,300
openai/cli/_api/chat/__pycache__/__init__.cpython-39.pyc,,
openai/cli/_api/chat/__pycache__/completions.cpython-39.pyc,,
openai/cli/_api/chat/completions.py,sha256=qCRj3SofMG-1RvEouFYgDRzmvVng5gWBkg0cfmd9MXA,5301
openai/cli/_api/completions.py,sha256=eF0vRA-cAPLhG86tdIXYnfj2od0tQa4l3bdJ-p6eGyQ,6411
openai/cli/_api/files.py,sha256=AgIWqZu5jEWODwvBdsgWYAmEfvgEqfZzG1jOAtSl-PY,2150
openai/cli/_api/image.py,sha256=VKMRqKCHkl4JO7uP7RLDZu8DzF6ddQgpr3n2v9EOEBk,4711
openai/cli/_api/models.py,sha256=pGmIGZToj3raGGpKvPSq_EVUR-dqg4Vi0PNfZH98D2E,1295
openai/cli/_cli.py,sha256=WxqTnhVVtfzX0z7hV5fcvd3hkihaUgwOWpXOwyCS4Fc,6743
openai/cli/_errors.py,sha256=OpvFKu_eRPEOGlq30e6zoPVDMdm6ocPyALgwWx4SaeI,482
openai/cli/_models.py,sha256=tgsldjG216KpwgAZ5pS0sV02FQvONDJU2ElA4kCCiIU,491
openai/cli/_progress.py,sha256=buv3H5bEevj4sQ4AXQ8qG75NHqewx5hkJFUy4_3rBx0,1399
openai/cli/_tools/__init__.py,sha256=cj92MZq-9_1PQM8A4TQVsqKn5mcTDAGxHllJ0UvJOPE,58
openai/cli/_tools/__pycache__/__init__.cpython-39.pyc,,
openai/cli/_tools/__pycache__/_main.cpython-39.pyc,,
openai/cli/_tools/__pycache__/fine_tunes.cpython-39.pyc,,
openai/cli/_tools/__pycache__/migrate.cpython-39.pyc,,
openai/cli/_tools/_main.py,sha256=pakjEXHRHqYlTml-RxV7fNrRtRXzmZBinoPi1AJipFY,467
openai/cli/_tools/fine_tunes.py,sha256=RQgYMzifk6S7Y1I1K6huqco2QxmXa7gVUlHl6SrKTSU,1543
openai/cli/_tools/migrate.py,sha256=_M7Uoi_uh_jLPM_NcZQORf3bZ_paTVpkfH7bVzEIxjs,4875
openai/cli/_utils.py,sha256=SvhD3IAvxGv8-fxK4wh2h0EVnoai3RVdyqFuRZ9sklc,849
openai/lib/__pycache__/_validators.cpython-39.pyc,,
openai/lib/__pycache__/azure.cpython-39.pyc,,
openai/lib/_validators.py,sha256=99n6DUCpP8T08y2Q2Xz4tXiPP71eeNwGxZhE0Skldjc,35212
openai/lib/azure.py,sha256=7_kjzNpWtXY0dQqAvziz9n0vHScueVf5mAuVTSaE-yM,17379
openai/pagination.py,sha256=9q90iPs810LJNVp3Ni_AwrQvRvaq-4VpYCVixxeBeLY,2570
openai/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai/resources/__init__.py,sha256=E1xymmmfDW3WDuhSMaHbRxcK8C6SZ3W1pONTtbxKDP0,2419
openai/resources/__pycache__/__init__.cpython-39.pyc,,
openai/resources/__pycache__/completions.cpython-39.pyc,,
openai/resources/__pycache__/edits.cpython-39.pyc,,
openai/resources/__pycache__/embeddings.cpython-39.pyc,,
openai/resources/__pycache__/files.cpython-39.pyc,,
openai/resources/__pycache__/fine_tunes.cpython-39.pyc,,
openai/resources/__pycache__/images.cpython-39.pyc,,
openai/resources/__pycache__/models.cpython-39.pyc,,
openai/resources/__pycache__/moderations.cpython-39.pyc,,
openai/resources/audio/__init__.py,sha256=S6KnDfb5_myivFeh-PM_xhq2bXpz0ljzvAuX0WvFxHo,789
openai/resources/audio/__pycache__/__init__.cpython-39.pyc,,
openai/resources/audio/__pycache__/audio.cpython-39.pyc,,
openai/resources/audio/__pycache__/transcriptions.cpython-39.pyc,,
openai/resources/audio/__pycache__/translations.cpython-39.pyc,,
openai/resources/audio/audio.py,sha256=AnFHF3a27AFsBDheRl_YP5Ue6oVy8xn9r1QRPlSMqbs,1879
openai/resources/audio/transcriptions.py,sha256=X6H7AN2JJWFElgMOn6ZhyMGx1ZhqWdMNGaJVDrcCLX4,8786
openai/resources/audio/translations.py,sha256=p1SpLcXDUj7eYCHIV07XfkFyf8BEeeNoS7JNPUd0MzA,7934
openai/resources/chat/__init__.py,sha256=8zjX-HmXak-XvoPdstIV-uUF49SBHlHzRrbv-WqeqKE,491
openai/resources/chat/__pycache__/__init__.cpython-39.pyc,,
openai/resources/chat/__pycache__/chat.cpython-39.pyc,,
openai/resources/chat/__pycache__/completions.cpython-39.pyc,,
openai/resources/chat/chat.py,sha256=ysGF5N1Gv6RcWmmqflnfN3rqcUqHjml53j_ruO6a7J0,1337
openai/resources/chat/completions.py,sha256=IyEYYPCVf1oqKfGCgZ253y14NHtdAYuPelNgShS79ks,46617
openai/resources/completions.py,sha256=rh7AgPgXVKyb1JoGCcYeFU3HcI7o2JQCxXVzzj4zbSA,55522
openai/resources/edits.py,sha256=Tdc6QUqkttfLRG9xMKi3QK7W9cfa9OvJhqlCbovA6LM,7755
openai/resources/embeddings.py,sha256=WifD46VcdYRC0x3msaLeBXaXUQS7vDBo3sGwf7cIxeA,9136
openai/resources/files.py,sha256=_bb8_XRwRa2bQKsCaVf3qbNALZG9W4fveGdkhRxhlrM,17478
openai/resources/fine_tunes.py,sha256=NdgOqLkVGXCap2YJdnTLW2bgexQJKdUKiVpTmz6GuDI,37043
openai/resources/fine_tuning/__init__.py,sha256=r31AXYyHoC42T8WfUrM7tGahQcU4T5MXd3SVxwL2UEE,483
openai/resources/fine_tuning/__pycache__/__init__.cpython-39.pyc,,
openai/resources/fine_tuning/__pycache__/fine_tuning.cpython-39.pyc,,
openai/resources/fine_tuning/__pycache__/jobs.cpython-39.pyc,,
openai/resources/fine_tuning/fine_tuning.py,sha256=29_e1ff_qDd5gToWNXQBOhYbWkM0e_Z2yWdJnNbm8Tw,1283
openai/resources/fine_tuning/jobs.py,sha256=34PAlEdLiirCM3GUKygu1ta_EnntSga_FlI9-icNjOM,21748
openai/resources/images.py,sha256=l6CNDu_zlJlOo44cQZNtV3neRE64v0Aa30qmd8T1w1A,19472
openai/resources/models.py,sha256=hy_vsBpbRoG3BpDvLoRFWOKJXlQd8MnnVwhZpxUks9c,8465
openai/resources/moderations.py,sha256=cuGkLFo-DFA1rnPRbYcpfiBQomP5CysC_TUZ7cS0x7w,5744
openai/types/__init__.py,sha256=WE31HfA6r9tw3gyqmBtWO_EdJmoLn-pGn46YeJG1n28,1990
openai/types/__pycache__/__init__.cpython-39.pyc,,
openai/types/__pycache__/completion.cpython-39.pyc,,
openai/types/__pycache__/completion_choice.cpython-39.pyc,,
openai/types/__pycache__/completion_create_params.cpython-39.pyc,,
openai/types/__pycache__/completion_usage.cpython-39.pyc,,
openai/types/__pycache__/create_embedding_response.cpython-39.pyc,,
openai/types/__pycache__/edit.cpython-39.pyc,,
openai/types/__pycache__/edit_create_params.cpython-39.pyc,,
openai/types/__pycache__/embedding.cpython-39.pyc,,
openai/types/__pycache__/embedding_create_params.cpython-39.pyc,,
openai/types/__pycache__/file_content.cpython-39.pyc,,
openai/types/__pycache__/file_create_params.cpython-39.pyc,,
openai/types/__pycache__/file_deleted.cpython-39.pyc,,
openai/types/__pycache__/file_object.cpython-39.pyc,,
openai/types/__pycache__/fine_tune.cpython-39.pyc,,
openai/types/__pycache__/fine_tune_create_params.cpython-39.pyc,,
openai/types/__pycache__/fine_tune_event.cpython-39.pyc,,
openai/types/__pycache__/fine_tune_events_list_response.cpython-39.pyc,,
openai/types/__pycache__/fine_tune_list_events_params.cpython-39.pyc,,
openai/types/__pycache__/image.cpython-39.pyc,,
openai/types/__pycache__/image_create_variation_params.cpython-39.pyc,,
openai/types/__pycache__/image_edit_params.cpython-39.pyc,,
openai/types/__pycache__/image_generate_params.cpython-39.pyc,,
openai/types/__pycache__/images_response.cpython-39.pyc,,
openai/types/__pycache__/model.cpython-39.pyc,,
openai/types/__pycache__/model_deleted.cpython-39.pyc,,
openai/types/__pycache__/moderation.cpython-39.pyc,,
openai/types/__pycache__/moderation_create_params.cpython-39.pyc,,
openai/types/__pycache__/moderation_create_response.cpython-39.pyc,,
openai/types/audio/__init__.py,sha256=0Goc0Q-sdLA1S581SLm4A9k12xo91bf6Et05asulmc0,404
openai/types/audio/__pycache__/__init__.cpython-39.pyc,,
openai/types/audio/__pycache__/transcription.cpython-39.pyc,,
openai/types/audio/__pycache__/transcription_create_params.cpython-39.pyc,,
openai/types/audio/__pycache__/translation.cpython-39.pyc,,
openai/types/audio/__pycache__/translation_create_params.cpython-39.pyc,,
openai/types/audio/transcription.py,sha256=bHHqJzRrdk_p23yvenTGjw0QBnjI3ebem1Ji1IJROOc,164
openai/types/audio/transcription_create_params.py,sha256=VlodHeT0pFzAOPn8RtBvGV_l9jYnWPL3mwusHhUJqMk,1684
openai/types/audio/translation.py,sha256=qOh5RFGsAjX5fid6vFYtdNNO_7QVtAAZWzHwbcNc224,160
openai/types/audio/translation_create_params.py,sha256=yVaacLkjtpi7vSNdpA0mTBwG7hhPdmEHCWJBS4gFY9k,1394
openai/types/chat/__init__.py,sha256=sLLctuCYOmAbF1Uci96pISeXT_FCbTqmw-ClgGc0Oy4,585
openai/types/chat/__pycache__/__init__.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_chunk.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_message.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_message_param.cpython-39.pyc,,
openai/types/chat/__pycache__/chat_completion_role.cpython-39.pyc,,
openai/types/chat/__pycache__/completion_create_params.cpython-39.pyc,,
openai/types/chat/chat_completion.py,sha256=2lr7DJ6flIkawnmlNWBNov5q9X-UDSOXCRX7Oeg-Chw,1537
openai/types/chat/chat_completion_chunk.py,sha256=gTg_UxdXkoLAKc4vCH41rHA0x4ZtZ6jac73OiIp3zkY,2366
openai/types/chat/chat_completion_message.py,sha256=P9QqJtFWbPekCliZTvZB1JQloumc7WAApCKtNw_WiIE,988
openai/types/chat/chat_completion_message_param.py,sha256=SyfRu7c0vJWuBS29543Y6J5_KJu1vbxfNeAGNIGPR44,1544
openai/types/chat/chat_completion_role.py,sha256=UutKgmnIWtRBiZwttNaPM_c0R4CKZ_rzMLizGAF7dto,199
openai/types/chat/completion_create_params.py,sha256=QCB6tpM4ZCe0l-GYVbtPBAqV86HBr4YaXv0MFevQKv8,7097
openai/types/completion.py,sha256=yVwFXyHkiirOTyT2sR8p0sAm_S7dupNiF6r5tZiJeHM,786
openai/types/completion_choice.py,sha256=VPHNMrM4xNSwxYiDIb-pnTAJJg9zab-TYr_Th-LuRsE,923
openai/types/completion_create_params.py,sha256=rJ3mA3Yq9Sv-exy-4CAe0c6xpqQQrBD2W2EOY01mURA,7183
openai/types/completion_usage.py,sha256=H1onC3vuVy5NKSUlW8FBfVzl_PfgFyrB_ytro80ZAjM,401
openai/types/create_embedding_response.py,sha256=wkeHjP-Kd7hCxBb7dI5vVQLL4l0R_BrcDvskQiKgCJk,720
openai/types/edit.py,sha256=wmQglC1vkIspdPRe95pIZCbaGhqcW2pxTrXnQh6x5T0,1117
openai/types/edit_create_params.py,sha256=mjV0iQomNFtIQqfadCI6Sggqp7wYQGb7GiQ-yhKNSGs,1454
openai/types/embedding.py,sha256=qbTz93y66nsTVc7aKugmxGLbvlpn7nx9xJwZd4yeZdY,549
openai/types/embedding_create_params.py,sha256=EXRdsBD4I48EuaCGxJLuLiBE0wEaeJdvSltThQwb7E0,1564
openai/types/file_content.py,sha256=GuNtqtZAuHFXS0uIJbTpOvzx_RePrE-lnH3FvNw4dJ8,100
openai/types/file_create_params.py,sha256=sg11F8_wupP6Wo9ZpTvN7EO5EqidQ7ElTpihbL7zNa4,716
openai/types/file_deleted.py,sha256=aR_v0VgSX19ogPbvtoNOoDxD0IsR8KcjXdkdhUfO76w,193
openai/types/file_object.py,sha256=Vd-1NBGaJ8xTdRz7ehaTqQ8VefBTLvZxZS92ArUM1j8,1017
openai/types/fine_tune.py,sha256=4S-mzDlYS18dcbHP2lJwqmQ_acwLQBKp1JvYosRm56A,2703
openai/types/fine_tune_create_params.py,sha256=yyMgO5R3jhvAWyTr4HvkTFTItjdnUHhl52UPavqIyfU,5591
openai/types/fine_tune_event.py,sha256=AKj3uOvfaJ_lowMYq0zdWPhZJ8mbO5wP-llCGaDUl4w,220
openai/types/fine_tune_events_list_response.py,sha256=kAUpXYZdA_zc3ZLa-eoVzMMqdvoKWMXNyM4pKXuIqaM,290
openai/types/fine_tune_list_events_params.py,sha256=aWTdLI8Wq_5yQ_0ulY0vRqltBFM2dMJahYc9XXXwyMg,1636
openai/types/fine_tuning/__init__.py,sha256=wfu8ciqHopBRPUkZ0D_NkP14-1wYFgVrY8pbCI2i84s,431
openai/types/fine_tuning/__pycache__/__init__.cpython-39.pyc,,
openai/types/fine_tuning/__pycache__/fine_tuning_job.cpython-39.pyc,,
openai/types/fine_tuning/__pycache__/fine_tuning_job_event.cpython-39.pyc,,
openai/types/fine_tuning/__pycache__/job_create_params.cpython-39.pyc,,
openai/types/fine_tuning/__pycache__/job_list_events_params.cpython-39.pyc,,
openai/types/fine_tuning/__pycache__/job_list_params.cpython-39.pyc,,
openai/types/fine_tuning/fine_tuning_job.py,sha256=BODqHJ8Ap9D_WJb7fFxdT116qeh5G6HsN29mLnFOby0,3166
openai/types/fine_tuning/fine_tuning_job_event.py,sha256=qW7xjEhAkEKYwkQRORkTdn9hsgPxZ6l_RZ0NoDDZVtc,312
openai/types/fine_tuning/job_create_params.py,sha256=cUPq5J39VpPGCk4l9FotmvmWhzAMamG-aeco8UTlCFg,2194
openai/types/fine_tuning/job_list_events_params.py,sha256=tfpo_bsTGW2ZaPzv5eJyr5F38UEUiwE_MJDNttfKLwo,367
openai/types/fine_tuning/job_list_params.py,sha256=Adm5vy-_0qoKP9Ubf_DOoT_pRkyB1YBdbvbAMAP40hw,363
openai/types/image.py,sha256=H8AIvKWPJq4LdEkRaFSMq58VPKz49R5t8qRJHzrdKmE,419
openai/types/image_create_variation_params.py,sha256=NTHPK4s7wGzJc41kUynh5PTFR65GEfifEF03qZOSVFg,1132
openai/types/image_edit_params.py,sha256=riRDaYQGJkecJF0FUNFEUjG7mRCWlYAF0ROiLXs3Bmc,1543
openai/types/image_generate_params.py,sha256=ohlmsElAHk3vKyZm8uPf8jTwSwmK_-yn9OMYPp46YMs,1059
openai/types/images_response.py,sha256=3eOiPIDxOSkxEaNrb0HOnHZmPtFYWQtY_xpC-yJN6U0,241
openai/types/model.py,sha256=2bnYjRclYjCWgcEyw-8NqdbD6ZfscOAATl-paEM2IAc,447
openai/types/model_deleted.py,sha256=vDZMiixtF903ce9bGixXpus5J1KrHYGEyu_Eb6kO-m8,195
openai/types/moderation.py,sha256=xSZnbax_E04ppN4dw9S7Pf8Y6dzIL9-W1uL26fn_tyk,3983
openai/types/moderation_create_params.py,sha256=VbmYPy75IFhWNeZGV_ocuM_m5w2XdL6ogIDLf0gAwos,921
openai/types/moderation_create_response.py,sha256=S2IXOkUwnlEWH8hKpMI8nPpg2NEiLYE3OfTXL892xvA,451
openai/version.py,sha256=cjbXKO8Ut3aiv4YlQnugff7AdC48MpSndcx96q88Yb8,62
